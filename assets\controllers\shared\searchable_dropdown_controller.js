import { Controller } from "@hotwired/stimulus";

/**
 * Controller Stimulus pour les dropdowns avec recherche
 */
export default class extends Controller {
    static targets = ['button', 'dropdown', 'hiddenInput', 'buttonText', 'searchInput'];
    static values = {
        selected: String,
        name: String,
        required: <PERSON><PERSON><PERSON>,
        placeholder: String
    };

    connect() {
        console.log('SearchableDropdownController connecté!', this.element);
        this.isOpen = false;
        this.allOptions = [];
        this.filteredOptions = [];
        this.setupEventListeners();

        // Initialiser avec la valeur sélectionnée si elle existe
        if (this.selectedValue) {
            this.selectOption(this.selectedValue, true);
        }

        // Initialiser le conteneur d'options s'il n'existe pas
        this.ensureOptionsContainer();
    }

    ensureOptionsContainer() {
        if (!this.hasDropdownTarget) return;

        let optionsContainer = this.dropdownTarget.querySelector('.options-container');
        if (!optionsContainer) {
            // Créer le conteneur d'options s'il n'existe pas
            optionsContainer = document.createElement('div');
            optionsContainer.className = 'options-container max-h-60 overflow-y-auto';
            this.dropdownTarget.appendChild(optionsContainer);
        }
    }

    disconnect() {
        document.removeEventListener('click', this.handleDocumentClick.bind(this));
    }

    setupEventListeners() {
        // Listener pour fermer le dropdown en cliquant ailleurs
        document.addEventListener('click', this.handleDocumentClick.bind(this));

        // Listener pour la recherche
        if (this.hasSearchInputTarget) {
            this.searchInputTarget.addEventListener('input', this.handleSearch.bind(this));
            this.searchInputTarget.addEventListener('keydown', this.handleSearchKeydown.bind(this));
        }
    }

    /**
     * Toggle l'ouverture/fermeture du dropdown
     */
    toggle() {
        if (this.isOpen) {
            this.close();
        } else {
            this.open();
        }
    }

    /**
     * Ouvre le dropdown
     */
    open() {
        this.dropdownTarget.classList.remove('hidden');
        this.isOpen = true;

        // Ajouter une classe active au bouton
        this.buttonTarget.classList.add('ring-4', 'ring-gray-100');

        // Focus sur le champ de recherche
        if (this.hasSearchInputTarget) {
            setTimeout(() => {
                this.searchInputTarget.focus();
            }, 100);
        }
    }

    /**
     * Ferme le dropdown
     */
    close() {
        this.dropdownTarget.classList.add('hidden');
        this.isOpen = false;

        // Retirer la classe active du bouton
        this.buttonTarget.classList.remove('ring-4', 'ring-gray-100');

        // Vider le champ de recherche
        if (this.hasSearchInputTarget) {
            this.searchInputTarget.value = '';
            this.showAllOptions();
        }
    }

    /**
     * Sélectionne une option
     */
    selectOption(event, skipClose = false) {
        let value, text;

        if (typeof event === 'string') {
            // Appelé programmatiquement avec une valeur
            value = event;
            const option = this.allOptions.find(opt => opt.value === value);
            if (option) {
                text = option.text;
            }
        } else {
            // Appelé par un clic sur une option
            const option = event.currentTarget;
            value = option.dataset.value || '';
            text = option.dataset.text || option.textContent.trim();
        }

        if (value === undefined) return;

        // Mettre à jour le bouton
        if (this.hasButtonTextTarget) {
            this.buttonTextTarget.textContent = text;
        }

        // Mettre à jour l'input caché
        if (this.hasHiddenInputTarget) {
            this.hiddenInputTarget.value = value;
            // Déclencher l'événement change pour la validation des formulaires
            this.hiddenInputTarget.dispatchEvent(new Event('change', { bubbles: true }));
        }

        // Mettre à jour les classes des options
        this.updateOptionStates(value);

        // Stocker la valeur sélectionnée
        this.selectedValue = value;

        if (!skipClose) {
            this.close();
        }
    }

    /**
     * Met à jour l'état visuel des options
     */
    updateOptionStates(selectedValue) {
        const options = this.dropdownTarget.querySelectorAll('[data-value]');
        options.forEach(option => {
            const optionValue = option.dataset.value || '';
            if (optionValue === selectedValue) {
                option.classList.add('bg-blue-50', 'text-blue-700');
                option.classList.remove('text-gray-700');
            } else {
                option.classList.remove('bg-blue-50', 'text-blue-700');
                option.classList.add('text-gray-700');
            }
        });
    }

    /**
     * Gère la recherche
     */
    handleSearch(event) {
        const searchTerm = event.target.value.toLowerCase();
        this.filterOptions(searchTerm);
    }

    /**
     * Filtre les options selon le terme de recherche
     */
    filterOptions(searchTerm) {
        if (!searchTerm) {
            this.showAllOptions();
            return;
        }

        this.filteredOptions = this.allOptions.filter(option =>
            option.text.toLowerCase().includes(searchTerm) ||
            (option.searchableText && option.searchableText.toLowerCase().includes(searchTerm))
        );

        this.renderOptions(this.filteredOptions);
    }

    /**
     * Affiche toutes les options
     */
    showAllOptions() {
        this.renderOptions(this.allOptions);
    }

    /**
     * Rend les options dans le dropdown
     */
    renderOptions(options) {
        this.ensureOptionsContainer();
        const optionsContainer = this.dropdownTarget.querySelector('.options-container');
        if (!optionsContainer) return;

        optionsContainer.innerHTML = '';

        if (options.length === 0) {
            const noResult = document.createElement('div');
            noResult.className = 'px-4 py-2 text-sm text-gray-500';
            noResult.textContent = 'Aucun résultat trouvé';
            optionsContainer.appendChild(noResult);
            return;
        }

        options.forEach(option => {
            const button = document.createElement('button');
            button.type = 'button';
            button.className = 'custom-dropdown-option';
            button.dataset.value = option.value;
            button.dataset.text = option.text;
            button.dataset.action = 'click->searchable-dropdown#selectOption';
            button.innerHTML = option.html || option.text;
            optionsContainer.appendChild(button);
        });

        // Mettre à jour l'état des options
        if (this.selectedValue) {
            this.updateOptionStates(this.selectedValue);
        }
    }

    /**
     * Met à jour les options disponibles
     */
    updateOptions(options) {
        this.allOptions = options;
        this.filteredOptions = [...options];
        this.showAllOptions();
    }

    /**
     * Gère les clics en dehors du dropdown
     */
    handleDocumentClick(event) {
        if (!this.element.contains(event.target)) {
            this.close();
        }
    }

    /**
     * Gère les touches du clavier dans le champ de recherche
     */
    handleSearchKeydown(event) {
        switch (event.key) {
            case 'Escape':
                this.close();
                break;
            case 'ArrowDown':
                event.preventDefault();
                this.focusFirstOption();
                break;
            case 'Enter':
                event.preventDefault();
                // Sélectionner la première option si elle existe
                const firstOption = this.dropdownTarget.querySelector('.custom-dropdown-option');
                if (firstOption) {
                    this.selectOption({ currentTarget: firstOption });
                }
                break;
        }
    }

    /**
     * Met le focus sur la première option
     */
    focusFirstOption() {
        const firstOption = this.dropdownTarget.querySelector('.custom-dropdown-option');
        if (firstOption) {
            firstOption.focus();
        }
    }

    /**
     * Réinitialise le dropdown
     */
    reset() {
        this.selectedValue = '';
        if (this.hasButtonTextTarget) {
            this.buttonTextTarget.textContent = this.placeholderValue || 'Sélectionner...';
        }
        if (this.hasHiddenInputTarget) {
            this.hiddenInputTarget.value = '';
        }
        if (this.hasSearchInputTarget) {
            this.searchInputTarget.value = '';
        }
        this.showAllOptions();
        this.updateOptionStates('');
    }
}
