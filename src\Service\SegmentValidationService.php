<?php

namespace App\Service;

use App\Entity\Segment;
use App\Entity\User;
use App\Repository\SegmentRepository;
use Doctrine\ORM\EntityManagerInterface;

class SegmentValidationService
{
    public function __construct(
        private EntityManagerInterface $entityManager,
        private SegmentRepository $segmentRepository
    ) {}

    /**
     * Valide un segment
     */
    public function validerSegment(Segment $segment, User $validateur): bool
    {
        if (!$validateur->canValidateSegments()) {
            throw new \InvalidArgumentException('L\'utilisateur n\'a pas les droits pour valider des segments');
        }

        // Vérifier si le statut de validation peut être modifié
        if (!$segment->canValidationStatusBeChanged()) {
            throw new \InvalidArgumentException('Le statut de validation de ce segment ne peut pas être modifié car des primes associées ont été validées par l\'assistante RH');
        }

        $segment->valider($validateur);
        $this->entityManager->flush();

        return true;
    }

    /**
     * Invalide un segment
     */
    public function invaliderSegment(Segment $segment, User $validateur): bool
    {
        if (!$validateur->canValidateSegments()) {
            throw new \InvalidArgumentException('L\'utilisateur n\'a pas les droits pour invalider des segments');
        }

        // Vérifier si le statut de validation peut être modifié
        if (!$segment->canValidationStatusBeChanged()) {
            throw new \InvalidArgumentException('Le statut de validation de ce segment ne peut pas être modifié car des primes associées ont été validées par l\'assistante RH');
        }

        $segment->invalider();
        $this->entityManager->flush();

        return true;
    }

    /**
     * Valide plusieurs segments en lot
     */
    public function validerSegmentsEnLot(array $segments, User $validateur): int
    {
        if (!$validateur->canValidateSegments()) {
            throw new \InvalidArgumentException('L\'utilisateur n\'a pas les droits pour valider des segments');
        }

        $count = 0;
        foreach ($segments as $segment) {
            if ($segment instanceof Segment && !$segment->isValide()) {
                $segment->valider($validateur);
                $count++;
            }
        }

        $this->entityManager->flush();
        return $count;
    }

    /**
     * Trouve les segments en attente de validation
     */
    public function getSegmentsEnAttenteValidation(): array
    {
        return $this->segmentRepository->findBy(['valide' => false]);
    }

    /**
     * Trouve les segments validés par un utilisateur
     */
    public function getSegmentsValidesParUtilisateur(User $validateur): array
    {
        return $this->segmentRepository->findBy(['validePar' => $validateur]);
    }

    /**
     * Trouve les segments d'un utilisateur en attente de validation
     */
    public function getSegmentsUtilisateurEnAttente(User $user): array
    {
        return $this->segmentRepository->findBy([
            'user' => $user,
            'valide' => false
        ]);
    }

    /**
     * Trouve les segments d'un utilisateur validés
     */
    public function getSegmentsUtilisateurValides(User $user): array
    {
        return $this->segmentRepository->findBy([
            'user' => $user,
            'valide' => true
        ]);
    }

    /**
     * Statistiques de validation
     */
    public function getStatistiquesValidation(): array
    {
        $qb = $this->entityManager->createQueryBuilder();

        // Total segments
        $total = $qb->select('COUNT(s.id)')
            ->from(Segment::class, 's')
            ->getQuery()
            ->getSingleScalarResult();

        // Segments validés
        $qb = $this->entityManager->createQueryBuilder();
        $valides = $qb->select('COUNT(s.id)')
            ->from(Segment::class, 's')
            ->where('s.valide = true')
            ->getQuery()
            ->getSingleScalarResult();

        // Segments en attente
        $enAttente = $total - $valides;

        return [
            'total' => (int) $total,
            'valides' => (int) $valides,
            'en_attente' => (int) $enAttente,
            'pourcentage_valides' => $total > 0 ? round(($valides / $total) * 100, 2) : 0
        ];
    }

    /**
     * Vérifie si tous les segments d'une mission sont validés
     */
    public function tousSegmentsMissionValides(int $missionId): bool
    {
        $qb = $this->entityManager->createQueryBuilder();

        $segmentsNonValides = $qb->select('COUNT(s.id)')
            ->from(Segment::class, 's')
            ->where('s.mission = :missionId')
            ->andWhere('s.valide = false OR s.valide IS NULL')
            ->setParameter('missionId', $missionId)
            ->getQuery()
            ->getSingleScalarResult();

        return $segmentsNonValides == 0;
    }

    /**
     * Vérifie si tous les segments d'un utilisateur sur une période sont validés
     */
    public function tousSegmentsUtilisateurPeriodeValides(User $user, \DateTimeInterface $debut, \DateTimeInterface $fin): bool
    {
        $qb = $this->entityManager->createQueryBuilder();

        $segmentsNonValides = $qb->select('COUNT(s.id)')
            ->from(Segment::class, 's')
            ->where('s.user = :user')
            ->andWhere('s.dateHeureDebut >= :debut')
            ->andWhere('s.dateHeureFin <= :fin')
            ->andWhere('s.valide = false OR s.valide IS NULL')
            ->setParameter('user', $user)
            ->setParameter('debut', $debut)
            ->setParameter('fin', $fin)
            ->getQuery()
            ->getSingleScalarResult();

        return $segmentsNonValides == 0;
    }

    /**
     * Vérifie si un segment peut être validé en fonction des primes associées
     */
    public function canBeValidated(Segment $segment): bool
    {
        return $segment->canValidationStatusBeChanged();
    }
}
