# Test de la fonctionnalité de validation des segments

## Fonctionnalités implémentées

### 1. Nouvelles méthodes dans l'entité Segment
- ✅ `canBeModified()` : Vérifie si un segment peut être modifié
- ✅ `canValidationStatusBeChanged()` : Vérifie si le statut de validation peut être modifié

### 2. Nouvelles routes API
- ✅ `POST /api/segments/{id}/validate` : Valide un segment
- ✅ `POST /api/segments/{id}/invalidate` : Invalide un segment

### 3. Contrôles de sécurité
- ✅ Vérification des droits avec `canValidateSegments()`
- ✅ Vérification des contraintes métier avant modification/suppression
- ✅ Empêche la modification si des primes associées sont validées par l'assistante RH

### 4. Interface utilisateur
- ✅ Boutons de validation stylés dans la modale du calendrier
- ✅ Indicateurs visuels sur les événements du calendrier (✓ et bordure verte)
- ✅ Affichage conditionnel des boutons selon les droits et contraintes

### 5. Logique métier
- ✅ Les segments sont modifiables tant que `segment.valide = false`
- ✅ Les segments ne sont plus modifiables si `segment.valide = true`
- ✅ Le champ `segment.valide` peut être modifié tant que `prime.validation_assistante_rh = false`
- ✅ Une fois `prime.validation_assistante_rh = true`, impossible de modifier les segments

## ✅ Correction effectuée

**Problème résolu** : L'erreur HTTP 405 était due à une mauvaise construction des URLs.
- **Avant** : `/api/segments/validate35` (incorrect)
- **Après** : `/api/segments/35/validate` (correct)

**Solution** : Utilisation de `replace('__ID__', segmentId)` au lieu de la concaténation.

## Tests à effectuer

### Test 1 : Validation d'un segment
1. Se connecter avec un compte responsable de mission ou super admin
2. Aller sur le calendrier (`/calendrier`)
3. Cliquer sur un segment non validé
4. Vérifier que le bouton "Valider" apparaît à gauche dans la modale
5. Cliquer sur "Valider"
6. Vérifier que le segment affiche maintenant ✓ et une bordure verte
7. Vérifier que le bouton "Invalider" apparaît maintenant

### Test 2 : Invalidation d'un segment
1. Cliquer sur un segment validé
2. Vérifier que le bouton "Invalider" apparaît
3. Cliquer sur "Invalider"
4. Vérifier que le segment n'affiche plus ✓ et a une bordure grise
5. Vérifier que le bouton "Valider" apparaît maintenant

### Test 3 : Contraintes de modification
1. Valider un segment
2. Créer une prime pour l'utilisateur et la mission correspondante
3. Valider la prime par l'assistante RH
4. Essayer de modifier le segment (drag & drop)
5. Vérifier qu'un message d'erreur apparaît
6. Essayer de changer le statut de validation
7. Vérifier que les boutons de validation n'apparaissent plus

### Test 4 : Droits d'accès
1. Se connecter avec un compte utilisateur simple
2. Aller sur le calendrier
3. Cliquer sur un segment
4. Vérifier que les boutons de validation n'apparaissent pas

### Test 5 : API
1. Tester les nouvelles routes API :
   - `POST /api/segments/{id}/validate`
   - `POST /api/segments/{id}/invalidate`
2. Vérifier les codes de retour et messages d'erreur appropriés

## Fichiers modifiés

### Backend
- `src/Entity/Segment.php` : Nouvelles méthodes de vérification
- `src/Controller/Api/SegmentController.php` : Nouvelles routes et contrôles
- `src/Service/SegmentValidationService.php` : Méthodes de validation améliorées
- `src/Repository/SegmentRepository.php` : Ajout des informations de validation dans les événements

### Frontend
- `templates/calendrier/index.html.twig` : Nouveaux boutons et URLs
- `assets/controllers/calendar/calendar_controller.js` : Nouvelles méthodes de validation

## Notes importantes

1. **Sécurité** : Toutes les opérations sont protégées par des vérifications de droits
2. **Cohérence** : Les contraintes métier sont respectées à tous les niveaux
3. **UX** : Les indicateurs visuels permettent de voir rapidement l'état des segments
4. **API** : Les nouvelles routes suivent les conventions REST existantes

## Prochaines étapes possibles

1. Ajouter des notifications toast au lieu d'alertes
2. Ajouter un historique des validations
3. Permettre la validation en lot depuis le calendrier
4. Ajouter des filtres par statut de validation
