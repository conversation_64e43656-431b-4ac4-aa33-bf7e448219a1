{% extends 'base.html.twig' %}

{% block title %}{{ mission.titre }}{% endblock %}

{% block body %}
<div class="container mx-auto px-4 py-8">
    <!-- En-tête -->
    <div class="bg-white shadow rounded-lg mb-6">
        <div class="px-4 py-5 sm:p-6">
            <div class="sm:flex sm:items-center sm:justify-between">
                <div class="sm:flex sm:items-center">
                    <div class="flex-shrink-0">
                        <div class="h-20 w-20 rounded-full bg-blue-500 flex items-center justify-center">
                            <span class="text-white text-2xl font-medium">{{ mission.niveau }}</span>
                        </div>
                    </div>
                    <div class="mt-4 sm:mt-0 sm:ml-6">
                        <h1 class="text-2xl font-bold text-gray-900">{{ mission.titre }}</h1>
                        <p class="text-sm text-gray-500">{{ mission.pays }}</p>
                        <div class="mt-2 flex items-center space-x-4">
                            <span class="text-sm text-gray-600">📅 {{ mission.dateDebut|date('d/m/Y') }} - {{ mission.dateFin|date('d/m/Y') }}</span>
                            <span class="text-sm text-gray-600">⏱️ {{ mission.dureeJours }} jour(s)</span>
                        </div>
                    </div>
                </div>
                <div class="mt-5 sm:mt-0">
                    <span class="inline-flex items-center px-3 py-2 rounded-full text-sm font-medium {% if mission.zone == 'EURO' %}bg-blue-100 text-blue-800{% else %}bg-green-100 text-green-800{% endif %}">
                        {{ mission.zone == 'EURO' ? '🇪🇺 Zone Euro' : '🌍 Hors Zone Euro' }}
                    </span>
                </div>
            </div>
        </div>
    </div>

    <!-- Informations détaillées -->
    <div class="grid grid-cols-1 gap-6 lg:grid-cols-3">
        <!-- Informations de la mission -->
        <div class="bg-white shadow rounded-lg">
            <div class="px-4 py-5 sm:p-6">
                <h3 class="text-lg font-medium text-gray-900 mb-4">Informations</h3>
                <dl class="space-y-3">
                    <div>
                        <dt class="text-sm font-medium text-gray-500">Niveau</dt>
                        <dd class="text-sm text-gray-900">Niveau {{ mission.niveau }}</dd>
                    </div>
                    <div>
                        <dt class="text-sm font-medium text-gray-500">Zone</dt>
                        <dd class="text-sm text-gray-900">
                            {% if mission.zone == 'EURO' %}
                                Zone Euro
                            {% else %}
                                Hors Zone Euro
                            {% endif %}
                        </dd>
                    </div>
                    <div>
                        <dt class="text-sm font-medium text-gray-500">Durée</dt>
                        <dd class="text-sm text-gray-900">{{ mission.dureeJours }} jour(s)</dd>
                    </div>
                    <div>
                        <dt class="text-sm font-medium text-gray-500">Période</dt>
                        <dd class="text-sm text-gray-900">{{ mission.dateDebut|date('d/m/Y') }} - {{ mission.dateFin|date('d/m/Y') }}</dd>
                    </div>
                </dl>
            </div>
        </div>

        <!-- Utilisateurs assignés -->
        <div class="bg-white shadow rounded-lg">
            <div class="px-4 py-5 sm:p-6">
                <h3 class="text-lg font-medium text-gray-900 mb-4">Utilisateurs assignés ({{ mission.users|length }})</h3>
                {% if mission.users|length > 0 %}
                    <div class="space-y-3">
                        {% for user in mission.users %}
                            <div class="flex items-center justify-between">
                                <div class="flex items-center">
                                    <div class="flex-shrink-0 h-8 w-8">
                                        <div class="h-8 w-8 rounded-full bg-blue-500 flex items-center justify-center">
                                            <span class="text-white text-xs font-medium">{{ user.prenom|first }}{{ user.nom|first }}</span>
                                        </div>
                                    </div>
                                    <div class="ml-3">
                                        <p class="text-sm font-medium text-gray-900">{{ user.nomComplet }}</p>
                                        <p class="text-xs text-gray-500">{{ user.roleDisplay }}</p>
                                    </div>
                                </div>
                                <a href="{{ path('app_user_detail', {id: user.id}) }}" class="text-blue-600 hover:text-blue-900 text-xs">
                                    Voir profil
                                </a>
                            </div>
                        {% endfor %}
                    </div>

                {% else %}
                    <p class="text-sm text-gray-500">Aucun utilisateur assigné</p>
                {% endif %}
            </div>
        </div>

        <!-- Statistiques -->
        <div class="bg-white shadow rounded-lg">
            <div class="px-4 py-5 sm:p-6">
                <h3 class="text-lg font-medium text-gray-900 mb-4">Statistiques</h3>
                <dl class="space-y-3">
                    <div>
                        <dt class="text-sm font-medium text-gray-500">Segments</dt>
                        <dd class="text-2xl font-bold text-blue-600">{{ mission.segments|length }}</dd>
                    </div>
                    <div>
                        <dt class="text-sm font-medium text-gray-500">Heures totales</dt>
                        <dd class="text-lg text-gray-900">
                            {% set totalHeures = 0 %}
                            {% for segment in mission.segments %}
                                {% set totalHeures = totalHeures + segment.dureeHeures %}
                            {% endfor %}
                            {{ totalHeures|number_format(1) }}h
                        </dd>
                    </div>
                    <div>
                        <dt class="text-sm font-medium text-gray-500">Statut</dt>
                        <dd class="text-sm text-gray-900">
                            {% set now = date() %}
                            {% if mission.dateDebut > now %}
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                                    À venir
                                </span>
                            {% elseif mission.dateFin < now %}
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                                    Terminée
                                </span>
                            {% else %}
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                    En cours
                                </span>
                            {% endif %}
                        </dd>
                    </div>
                </dl>
            </div>
        </div>
    </div>

    <!-- Segments récents -->
    {% if mission.segments|length > 0 %}
        <div class="mt-6 bg-white shadow rounded-lg">
            <div class="px-4 py-5 sm:p-6">
                <h3 class="text-lg font-medium text-gray-900 mb-4">Segments récents</h3>
                <div class="overflow-hidden shadow ring-1 ring-black ring-opacity-5 md:rounded-lg">
                    <table class="min-w-full divide-y divide-gray-300">
                        <thead class="bg-gray-50">
                            <tr>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Date</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Utilisateur</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Durée</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Type</th>
                            </tr>
                        </thead>
                        <tbody class="bg-white divide-y divide-gray-200">
                            {% for segment in mission.segments|slice(0, 10) %}
                                <tr>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                        {{ segment.dateHeureDebut|date('d/m/Y H:i') }}
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                        {% if segment.user %}
                                            {{ segment.user.nomComplet }}
                                        {% else %}
                                            <span class="text-gray-500">Non assigné</span>
                                        {% endif %}
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                        {{ segment.dureeHeures|number_format(1) }}h
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                            Travail
                                        </span>
                                    </td>
                                </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    {% endif %}

    <!-- Actions -->
    <div class="mt-6 flex justify-between">
        <a href="{{ path('app_missions') }}" class="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
            ← Retour aux missions
        </a>
        {% if app.user.canCreateMissions() %}
        <div class="space-x-3">
            <button onclick="editMission({{ mission.id }})" class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700">
                Modifier
            </button>
            <button onclick="deleteMission({{ mission.id }}, '{{ mission.titre }}')" class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-red-600 hover:bg-red-700">
                Supprimer
            </button>
        </div>
        {% endif %}
    </div>
</div>

<script>
// Rediriger vers la page des missions avec les modals
function editMission(id) {
    window.location.href = `{{ path('app_missions') }}?edit=${id}`;
}

function deleteMission(id, titre) {
    if (confirm(`Êtes-vous sûr de vouloir supprimer la mission "${titre}" ?\n\nCette action est irréversible.`)) {
        window.location.href = `{{ path('app_missions') }}?delete=${id}`;
    }
}
</script>
{% endblock %}
