<?php

namespace App\Repository;

use App\Entity\Segment;
use App\Entity\Mission;
use App\Entity\User;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;

/**
 * @extends ServiceEntityRepository<Segment>
 */
class SegmentRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, Segment::class);
    }

    public function save(Segment $entity, bool $flush = false): void
    {
        $this->getEntityManager()->persist($entity);

        if ($flush) {
            $this->getEntityManager()->flush();
        }
    }

    public function remove(Segment $entity, bool $flush = false): void
    {
        $this->getEntityManager()->remove($entity);

        if ($flush) {
            $this->getEntityManager()->flush();
        }
    }

    /**
     * Trouve les segments par type
     */
    public function findByType(string $type): array
    {
        return $this->createQueryBuilder('s')
            ->andWhere('s.type = :type')
            ->setParameter('type', $type)
            ->orderBy('s.dateHeureDebut', 'DESC')
            ->getQuery()
            ->getResult();
    }

    /**
     * Trouve les segments d'une mission
     */
    public function findByMission(Mission $mission): array
    {
        return $this->createQueryBuilder('s')
            ->andWhere('s.mission = :mission')
            ->setParameter('mission', $mission)
            ->orderBy('s.dateHeureDebut', 'ASC')
            ->getQuery()
            ->getResult();
    }



    /**
     * Trouve les segments dans une période
     */
    public function findInPeriod(\DateTimeInterface $debut, \DateTimeInterface $fin): array
    {
        return $this->createQueryBuilder('s')
            ->andWhere('(s.dateHeureDebut BETWEEN :debut AND :fin) OR (s.dateHeureFin BETWEEN :debut AND :fin) OR (s.dateHeureDebut <= :debut AND s.dateHeureFin >= :fin)')
            ->setParameter('debut', $debut)
            ->setParameter('fin', $fin)
            ->orderBy('s.dateHeureDebut', 'ASC')
            ->getQuery()
            ->getResult();
    }

    /**
     * Trouve les segments de voyage
     */
    public function findVoyages(): array
    {
        return $this->findByType(Segment::TYPE_VOYAGE);
    }

    /**
     * Trouve les segments d'intervention
     */
    public function findInterventions(): array
    {
        return $this->findByType(Segment::TYPE_INTERVENTION);
    }

    /**
     * Trouve les segments de stand-by
     */
    public function findStandBy(): array
    {
        return $this->findByType(Segment::TYPE_STAND_BY);
    }

    /**
     * Trouve les segments hors plages normales
     */
    public function findHorsPlagesNormales(): array
    {
        return $this->createQueryBuilder('s')
            ->andWhere('(HOUR(s.dateHeureDebut) < 9 OR HOUR(s.dateHeureDebut) >= 18 OR (HOUR(s.dateHeureDebut) >= 12 AND HOUR(s.dateHeureDebut) < 14))')
            ->orderBy('s.dateHeureDebut', 'DESC')
            ->getQuery()
            ->getResult();
    }

    /**
     * Trouve les segments de week-end
     */
    public function findWeekend(): array
    {
        return $this->createQueryBuilder('s')
            ->andWhere('DAYOFWEEK(s.dateHeureDebut) IN (1, 7)') // 1 = dimanche, 7 = samedi
            ->orderBy('s.dateHeureDebut', 'DESC')
            ->getQuery()
            ->getResult();
    }

    /**
     * Calcule le total d'heures par type pour un utilisateur
     */
    public function getTotalHeuresParType(User $user, \DateTimeInterface $debut = null, \DateTimeInterface $fin = null): array
    {
        $qb = $this->createQueryBuilder('s')
            ->select('s.type, SUM(TIMESTAMPDIFF(HOUR, s.dateHeureDebut, s.dateHeureFin)) as totalHeures')
            ->andWhere('s.user = :user')
            ->setParameter('user', $user)
            ->groupBy('s.type');

        if ($debut) {
            $qb->andWhere('s.dateHeureDebut >= :debut')
               ->setParameter('debut', $debut);
        }

        if ($fin) {
            $qb->andWhere('s.dateHeureFin <= :fin')
               ->setParameter('fin', $fin);
        }

        return $qb->getQuery()->getResult();
    }

    /**
     * Trouve les segments pour le calendrier (format FullCalendar)
     */
    public function findForCalendar(\DateTimeInterface $debut, \DateTimeInterface $fin): array
    {
        $segments = $this->findInPeriod($debut, $fin);

        $events = [];

        foreach ($segments as $segment) {
            // Déterminer le nom de l'utilisateur
            $userName = 'Non assigné';
            if ($segment->getUser()) {
                $userName = $segment->getUser()->getNomComplet();
            }

            // Titre avec type et utilisateur
            $title = $segment->getType() . ' - ' . $userName;

            // Ajouter un indicateur de validation au titre
            $validationIndicator = $segment->isValide() ? ' ✅' : '';
            $titleWithValidation = $title . $validationIndicator;

            $events[] = [
                'id' => $segment->getId(),
                'title' => $titleWithValidation,
                'start' => $segment->getDateHeureDebut()->format('c'),
                'end' => $segment->getDateHeureFin()->format('c'),
                'backgroundColor' => $this->getColorByType($segment->getType()),
                'extendedProps' => [
                    'type' => $segment->getType(),
                    'userId' => $segment->getUser() ? $segment->getUser()->getId() : null,
                    'missionId' => $segment->getMission()->getId(),
                    'mission' => $segment->getMission()->getTitre(),
                    'userName' => $userName,
                    'pays' => $segment->getMission()->getPays(),
                    'valide' => $segment->isValide(),
                    'canBeModified' => $segment->canBeModified(),
                    'canValidationStatusBeChanged' => $segment->canValidationStatusBeChanged()
                ]
            ];
        }

        return $events;
    }

    public function findForCalendarByUser(User $user, \DateTimeInterface $debut, \DateTimeInterface $fin): array
    {
        $segments = $this->findByUserAndPeriod($user, $debut, $fin);

        $events = [];

        foreach ($segments as $segment) {
            // Titre avec type et utilisateur
            $title = $segment->getType();

            // Ajouter un indicateur de validation au titre
            $validationIndicator = $segment->isValide() ? ' ✅' : '';
            $titleWithValidation = $title . $validationIndicator;

            $events[] = [
                'id' => $segment->getId(),
                'title' => $titleWithValidation,
                'start' => $segment->getDateHeureDebut()->format('c'),
                'end' => $segment->getDateHeureFin()->format('c'),
                'backgroundColor' => $this->getColorByType($segment->getType()),
                'extendedProps' => [
                    'type' => $segment->getType(),
                    'userId' => $segment->getUser() ? $segment->getUser()->getId() : null,
                    'missionId' => $segment->getMission()->getId(),
                    'mission' => $segment->getMission()->getTitre(),
                    'userName' => $user->getNomComplet(),
                    'pays' => $segment->getMission()->getPays(),
                    'valide' => $segment->isValide(),
                    'canBeModified' => $segment->canBeModified(),
                    'canValidationStatusBeChanged' => $segment->canValidationStatusBeChanged()
                ]
            ];
        }

        return $events;
    }

    /**
     * Trouve les segments pour le calendrier de plusieurs utilisateurs spécifiques
     */
    public function findForCalendarByUserIds(array $userIds, \DateTimeInterface $debut, \DateTimeInterface $fin): array
    {
        if (empty($userIds)) {
            return [];
        }

        $segments = $this->createQueryBuilder('s')
            ->leftJoin('s.user', 'u')
            ->leftJoin('s.mission', 'm')
            ->andWhere('s.user IN (:userIds)')
            ->andWhere('(s.dateHeureDebut BETWEEN :debut AND :fin) OR (s.dateHeureFin BETWEEN :debut AND :fin) OR (s.dateHeureDebut <= :debut AND s.dateHeureFin >= :fin)')
            ->setParameter('userIds', $userIds)
            ->setParameter('debut', $debut)
            ->setParameter('fin', $fin)
            ->orderBy('s.dateHeureDebut', 'ASC')
            ->getQuery()
            ->getResult();

        $events = [];

        foreach ($segments as $segment) {
            // Déterminer le nom de l'utilisateur
            $userName = 'Non assigné';
            if ($segment->getUser()) {
                $userName = $segment->getUser()->getNomComplet();
            }

            // Titre avec type et utilisateur
            $title = $segment->getType() . ' - ' . $userName;

            // Ajouter un indicateur de validation au titre
            $validationIndicator = $segment->isValide() ? ' ✅' : '';
            $titleWithValidation = $title . $validationIndicator;

            $events[] = [
                'id' => $segment->getId(),
                'title' => $titleWithValidation,
                'start' => $segment->getDateHeureDebut()->format('c'),
                'end' => $segment->getDateHeureFin()->format('c'),
                'backgroundColor' => $this->getColorByType($segment->getType()),
                'extendedProps' => [
                    'type' => $segment->getType(),
                    'userId' => $segment->getUser() ? $segment->getUser()->getId() : null,
                    'missionId' => $segment->getMission()->getId(),
                    'mission' => $segment->getMission()->getTitre(),
                    'userName' => $userName,
                    'pays' => $segment->getMission()->getPays(),
                    'valide' => $segment->isValide(),
                    'canBeModified' => $segment->canBeModified(),
                    'canValidationStatusBeChanged' => $segment->canValidationStatusBeChanged()
                ]
            ];
        }

        return $events;
    }

    /**
     * Trouve les segments d'un utilisateur pour une période
     */
    public function findByUserAndPeriod(User $user, \DateTimeInterface $debut, \DateTimeInterface $fin): array
    {
        return $this->createQueryBuilder('s')
            ->andWhere('s.user = :user')
            ->andWhere('(s.dateHeureDebut BETWEEN :debut AND :fin) OR (s.dateHeureFin BETWEEN :debut AND :fin) OR (s.dateHeureDebut <= :debut AND s.dateHeureFin >= :fin)')
            ->setParameter('user', $user)
            ->setParameter('debut', $debut)
            ->setParameter('fin', $fin)
            ->orderBy('s.dateHeureDebut', 'ASC')
            ->getQuery()
            ->getResult();
    }

    /**
     * Trouve tous les segments d'un utilisateur
     */
    public function findByUser(User $user): array
    {
        return $this->createQueryBuilder('s')
            ->andWhere('s.user = :user')
            ->setParameter('user', $user)
            ->orderBy('s.dateHeureDebut', 'ASC')
            ->getQuery()
            ->getResult();
    }

    /**
     * Trouve tous les utilisateurs qui ont des segments
     */
    public function findUsersWithSegments(): array
    {
        return $this->createQueryBuilder('s')
            ->select('DISTINCT u')
            ->join('s.user', 'u')
            ->where('s.user IS NOT NULL')
            ->orderBy('u.nom', 'ASC')
            ->addOrderBy('u.prenom', 'ASC')
            ->getQuery()
            ->getResult();
    }

    private function getColorByType(string $type): string
    {
        return match($type) {
            Segment::TYPE_VOYAGE => '#3B82F6',      // Bleu
            Segment::TYPE_INTERVENTION => '#10B981', // Vert
            Segment::TYPE_STAND_BY => '#F59E0B',     // Orange
            default => '#6B7280'                     // Gris
        };
    }
}
