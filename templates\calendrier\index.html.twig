{% extends 'base.html.twig' %}

{% block title %}<PERSON><PERSON><PERSON> - OSI Manager{% endblock %}

{% block stylesheets %}
    {{ parent() }}
    <link href='https://cdn.jsdelivr.net/npm/fullcalendar@6.1.10/index.global.min.css' rel='stylesheet' />
    <style>
        /* S'assurer que le modal passe au-dessus de FullCalendar */
        .fc {
            z-index: 1;
        }

        /* Styles pour le calendrier */
        #calendar {
            min-height: 600px;
        }

        /* Styles pour la modale */
        .modal-overlay {
            z-index: 9999;
            background-color: rgba(0, 0, 0, 0.5);
        }

        .modal-content {
            z-index: 10000;
        }

        /* Styles pour les dropdowns personnalisés (copiés de missions) */
        .custom-dropdown {
            position: relative;
        }

        .custom-dropdown-button {
            display: flex;
            align-items: center;
            justify-content: space-between;
            width: 100%;
            padding: 12px 16px;
            font-size: 14px;
            font-weight: 400;
            color: #374151;
            background-color: white;
            border: 1px solid #d1d5db;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.15s ease;
            text-align: left;
        }

        .custom-dropdown-button:hover {
            border-color: #9ca3af;
            box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
        }

        .custom-dropdown-button:focus {
            outline: none;
            border-color: #3b82f6;
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
        }

        .custom-dropdown-menu {
            position: absolute;
            top: 100%;
            left: 0;
            right: 0;
            z-index: 50;
            margin-top: 4px;
            background-color: white;
            border: 1px solid #e5e7eb;
            border-radius: 8px;
            box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
            max-height: 300px;
            overflow-y: auto;
        }

        .custom-dropdown-option {
            display: flex;
            align-items: center;
            width: 100%;
            padding: 12px 16px;
            font-size: 14px;
            color: #374151;
            cursor: pointer;
            transition: all 0.15s ease;
            border: none;
            background: none;
            text-align: left;
        }

        .custom-dropdown-option:hover {
            background-color: #f3f4f6;
        }

        .custom-dropdown-option:focus {
            outline: none;
            background-color: #f3f4f6;
        }

        .custom-dropdown-option.selected,
        .custom-dropdown-option.keyboard-focus {
            background-color: #dbeafe;
            color: #1d4ed8;
            font-weight: 500;
        }

        .custom-dropdown-icon {
            margin-right: 8px;
            flex-shrink: 0;
        }

        .custom-dropdown-chevron {
            margin-left: 8px;
            transition: transform 0.2s ease;
        }

        .custom-dropdown-button[aria-expanded="true"] .custom-dropdown-chevron {
            transform: rotate(180deg);
        }
    </style>
{% endblock %}

{% block body %}
<div class="px-4 py-6 sm:px-0"
     data-controller="calendar"
     data-calendar-current-user-value="{{ {
         'id': app.user.id,
         'nomComplet': app.user.nomComplet,
         'isUser': app.user.isUser(),
         'isResponsableMission': app.user.isResponsableMission(),
         'isSuperAdmin': app.user.isSuperAdmin(),
         'canCreateSegmentsForOthers': app.user.isResponsableMission() or app.user.isSuperAdmin()
     }|json_encode|e('html_attr') }}">

    {% if app.user.isResponsableMission() or app.user.isSuperAdmin() %}
        <!-- Sélecteur d'utilisateurs pour les responsables de mission -->
        <div class="mb-6 bg-white rounded-lg shadow-sm border border-gray-200 p-4">
            <div class="flex items-center justify-between mb-4">
                <h3 class="text-lg font-medium text-gray-900">
                    <svg class="w-5 h-5 inline mr-2 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-2.239"></path>
                    </svg>
                    Filtrer par utilisateurs
                </h3>
                <button type="button"
                        class="text-sm text-blue-600 hover:text-blue-800"
                        data-calendar-target="clearFiltersButton"
                        data-action="click->calendar#clearUserFilters">
                    Tout afficher
                </button>
            </div>

            <div class="custom-dropdown"
                 data-controller="multi-user-selector"
                 data-multi-user-selector-placeholder-value="Sélectionner des utilisateurs..."
                 data-calendar-target="userFilterDropdown"
                 data-action="userSelectionChanged->calendar#onUserSelectionChanged">
                <button type="button"
                        class="custom-dropdown-button w-full"
                        data-multi-user-selector-target="button"
                        data-action="click->multi-user-selector#toggle">
                    <div class="flex items-center justify-between">
                        <span data-multi-user-selector-target="buttonText">Tous les utilisateurs</span>
                        <svg class="custom-dropdown-chevron w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                        </svg>
                    </div>
                </button>
                <div class="custom-dropdown-menu hidden" data-multi-user-selector-target="dropdown">
                    <!-- Champ de recherche -->
                    <div class="p-2 border-b border-gray-200">
                        <input type="text"
                               data-multi-user-selector-target="searchInput"
                               placeholder="Rechercher un utilisateur..."
                               class="w-full px-3 py-2 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                    </div>
                    <!-- Container pour les options -->
                    <div class="options-container max-h-60 overflow-y-auto">
                        <!-- Options seront ajoutées dynamiquement -->
                    </div>
                </div>
                <input type="hidden" name="selectedUsers" data-multi-user-selector-target="hiddenInput">
            </div>

            <div class="mt-3">
                <div class="flex flex-wrap gap-2" data-multi-user-selector-target="selectedUsersDisplay">
                    <!-- Les utilisateurs sélectionnés seront affichés ici -->
                </div>
            </div>
        </div>
    {% endif %}
    <!-- En-tête -->
    <div class="sm:flex sm:items-center mb-8">
        <div class="sm:flex-auto">
            <h1 class="text-3xl font-bold text-gray-900">Calendrier des missions</h1>
            <p class="mt-2 text-gray-600">Gestion des segments de mission</p>
        </div>
        <div class="mt-4 sm:mt-0 sm:ml-16 sm:flex-none">
            {% if app.user.isResponsableMission() %}
            <button type="button" onclick="openSegmentModal()" class="inline-flex items-center justify-center rounded-md border border-transparent bg-blue-600 px-4 py-2 text-sm font-medium text-white shadow-sm hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 sm:w-auto">
                Ajouter des segments
            </button>
            {% elseif app.user.isUser() %}
            <button type="button" onclick="openSegmentModal()" class="inline-flex items-center justify-center rounded-md border border-transparent bg-blue-600 px-4 py-2 text-sm font-medium text-white shadow-sm hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 sm:w-auto">
                Ajouter mes segments
            </button>
            {% endif %}
        </div>
    </div>

    <!-- Légende -->
    <div class="bg-white shadow rounded-lg mb-6">
        <div class="px-4 py-5 sm:p-6">
            <h3 class="text-lg font-medium text-gray-900 mb-4">Légende</h3>
            <div class="flex flex-wrap gap-4">
                <div class="flex items-center">
                    <div class="w-4 h-4 bg-blue-500 rounded mr-2"></div>
                    <span class="text-sm text-gray-700">Voyage</span>
                </div>
                <div class="flex items-center">
                    <div class="w-4 h-4 bg-green-500 rounded mr-2"></div>
                    <span class="text-sm text-gray-700">Intervention</span>
                </div>
                <div class="flex items-center">
                    <div class="w-4 h-4 bg-orange-500 rounded mr-2"></div>
                    <span class="text-sm text-gray-700">Stand-by</span>
                </div>
            </div>
        </div>
    </div>

    <!-- Calendrier -->
    <div class="bg-white shadow rounded-lg">
        <div class="px-4 py-5 sm:p-6">
            <div id="calendar" data-calendar-target="calendar"></div>
        </div>
    </div>

    <!-- Modal d'ajout/modification de segment -->
    <div data-calendar-target="modal"
         data-action="click->calendar#closeModalOnBackdrop"
         class="modal-overlay fixed inset-0 overflow-y-auto h-full w-full hidden">
        <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white modal-content"
             onclick="event.stopPropagation()">
            <div class="mt-3">
                <!-- En-tête de la modale -->
                <div class="flex items-center justify-between mb-4">
                    <h3 class="text-lg font-medium text-gray-900" data-calendar-target="modalTitle">
                        Ajouter un segment
                    </h3>
                    <button type="button"
                            data-action="click->calendar#closeModal"
                            class="text-gray-400 hover:text-gray-600">
                        <svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                        </svg>
                    </button>
                </div>

                <!-- Formulaire -->
                <form data-calendar-target="form" data-action="submit->calendar#submitForm">
                    <input type="hidden" data-calendar-target="segmentId" name="segmentId">

                    <div class="space-y-4">
                        <!-- Utilisateur -->
                        <div>
                            <label class="flex items-center text-sm font-medium text-gray-700 mb-2">
                                <svg class="w-4 h-4 mr-2 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                                </svg>
                                Utilisateur *
                            </label>
                            <div class="custom-dropdown"
                                 data-controller="searchable-dropdown"
                                 data-searchable-dropdown-name-value="userId"
                                 data-searchable-dropdown-required-value="true"
                                 data-searchable-dropdown-placeholder-value="Sélectionner un utilisateur"
                                 data-calendar-target="userDropdown">
                                <button type="button"
                                        class="custom-dropdown-button"
                                        data-searchable-dropdown-target="button"
                                        data-action="click->searchable-dropdown#toggle keydown->searchable-dropdown#handleKeydown"
                                        aria-expanded="false">
                                    <div class="flex items-center">
                                        <span data-searchable-dropdown-target="buttonText">Sélectionner un utilisateur</span>
                                    </div>
                                    <svg class="custom-dropdown-chevron w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                                    </svg>
                                </button>
                                <div class="custom-dropdown-menu hidden" data-searchable-dropdown-target="dropdown">
                                    <!-- Champ de recherche -->
                                    <div class="p-2 border-b border-gray-200">
                                        <input type="text"
                                               data-searchable-dropdown-target="searchInput"
                                               placeholder="Rechercher un utilisateur..."
                                               class="w-full px-3 py-2 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                                    </div>
                                    <!-- Container pour les options -->
                                    <div class="options-container max-h-60 overflow-y-auto">
                                        <!-- Options seront ajoutées dynamiquement -->
                                    </div>
                                </div>
                                <input type="hidden" name="userId" data-searchable-dropdown-target="hiddenInput" required>
                            </div>
                        </div>

                        <!-- Mission -->
                        <div>
                            <label class="flex items-center text-sm font-medium text-gray-700 mb-2">
                                <svg class="w-4 h-4 mr-2 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 13.255A23.931 23.931 0 0112 15c-3.183 0-6.22-.62-9-1.745M16 6V4a2 2 0 00-2-2h-4a2 2 0 00-2-2v2m8 0V6a2 2 0 012 2v6a2 2 0 01-2 2H8a2 2 0 01-2-2V8a2 2 0 012-2h8z"></path>
                                </svg>
                                Mission *
                            </label>
                            <div class="custom-dropdown"
                                 data-controller="searchable-dropdown"
                                 data-searchable-dropdown-name-value="missionId"
                                 data-searchable-dropdown-required-value="true"
                                 data-searchable-dropdown-placeholder-value="Sélectionner une mission"
                                 data-calendar-target="missionDropdown">
                                <button type="button"
                                        class="custom-dropdown-button"
                                        data-searchable-dropdown-target="button"
                                        data-action="click->searchable-dropdown#toggle keydown->searchable-dropdown#handleKeydown"
                                        aria-expanded="false">
                                    <div class="flex items-center">
                                        <span data-searchable-dropdown-target="buttonText">Sélectionner une mission</span>
                                    </div>
                                    <svg class="custom-dropdown-chevron w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                                    </svg>
                                </button>
                                <div class="custom-dropdown-menu hidden" data-searchable-dropdown-target="dropdown">
                                    <!-- Champ de recherche -->
                                    <div class="p-2 border-b border-gray-200">
                                        <input type="text"
                                               data-searchable-dropdown-target="searchInput"
                                               placeholder="Rechercher une mission..."
                                               class="w-full px-3 py-2 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                                    </div>
                                    <!-- Container pour les options -->
                                    <div class="options-container max-h-60 overflow-y-auto">
                                        <!-- Options seront ajoutées dynamiquement -->
                                    </div>
                                </div>
                                <input type="hidden" name="missionId" data-searchable-dropdown-target="hiddenInput" required>
                            </div>
                        </div>

                        <!-- Type -->
                        <div>
                            <label class="flex items-center text-sm font-medium text-gray-700 mb-2">
                                <svg class="w-4 h-4 mr-2 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z"></path>
                                </svg>
                                Type *
                            </label>
                            <div class="custom-dropdown"
                                 data-controller="custom-dropdown"
                                 data-custom-dropdown-name-value="type"
                                 data-custom-dropdown-required-value="true"
                                 data-calendar-target="typeDropdown">
                                <button type="button"
                                        class="custom-dropdown-button"
                                        data-custom-dropdown-target="button"
                                        data-action="click->custom-dropdown#toggle keydown->custom-dropdown#handleKeydown"
                                        aria-expanded="false">
                                    <div class="flex items-center">
                                        <span data-custom-dropdown-target="buttonText">Sélectionner un type</span>
                                    </div>
                                    <svg class="custom-dropdown-chevron w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                                    </svg>
                                </button>
                                <div class="custom-dropdown-menu hidden" data-custom-dropdown-target="dropdown">
                                    <button type="button" class="custom-dropdown-option" data-value="VOYAGE" data-text="Voyage" data-action="click->custom-dropdown#selectOption">
                                        <span class="custom-dropdown-icon">🚗</span>
                                        Voyage
                                    </button>
                                    <button type="button" class="custom-dropdown-option" data-value="INTERVENTION" data-text="Intervention" data-action="click->custom-dropdown#selectOption">
                                        <span class="custom-dropdown-icon">🔧</span>
                                        Intervention
                                    </button>
                                    <button type="button" class="custom-dropdown-option" data-value="STAND_BY" data-text="Stand-by" data-action="click->custom-dropdown#selectOption">
                                        <span class="custom-dropdown-icon">⏰</span>
                                        Stand-by
                                    </button>
                                </div>
                                <input type="hidden" name="type" data-custom-dropdown-target="hiddenInput" required>
                            </div>
                        </div>

                        <!-- Date de début -->
                        <div>
                            <label class="flex items-center text-sm font-medium text-gray-700 mb-2">
                                <svg class="w-4 h-4 mr-2 text-orange-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                </svg>
                                Date et heure de début *
                            </label>
                            <input type="datetime-local" data-calendar-target="dateHeureDebut" name="dateHeureDebut" required
                                   class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm">
                        </div>

                        <!-- Date de fin -->
                        <div>
                            <label class="flex items-center text-sm font-medium text-gray-700 mb-2">
                                <svg class="w-4 h-4 mr-2 text-orange-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                </svg>
                                Date et heure de fin *
                            </label>
                            <input type="datetime-local" data-calendar-target="dateHeureFin" name="dateHeureFin" required
                                   class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm">
                        </div>
                    </div>

                    <!-- Boutons d'action -->
                    <div class="flex justify-between items-center mt-6">
                        <!-- Boutons de validation (à gauche) -->
                        <div class="flex space-x-2" data-calendar-target="validationButtons" style="display: none;">
                            <button type="button"
                                    data-calendar-target="validateButton"
                                    data-action="click->calendar#validateSegment"
                                    class="inline-flex items-center px-3 py-2 text-sm font-medium text-white bg-green-600 rounded-md hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500">
                                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                </svg>
                                Valider
                            </button>
                            <button type="button"
                                    data-calendar-target="invalidateButton"
                                    data-action="click->calendar#invalidateSegment"
                                    class="inline-flex items-center px-3 py-2 text-sm font-medium text-white bg-orange-600 rounded-md hover:bg-orange-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-orange-500">
                                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                                </svg>
                                Invalider
                            </button>
                        </div>

                        <!-- Boutons principaux (à droite) -->
                        <div class="flex space-x-3">
                            <button type="button"
                                    data-action="click->calendar#closeModal"
                                    class="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 rounded-md hover:bg-gray-200">
                                Annuler
                            </button>
                            <button type="button"
                                    data-calendar-target="deleteButton"
                                    data-action="click->calendar#deleteSegment"
                                    class="px-4 py-2 text-sm font-medium text-white bg-red-600 rounded-md hover:bg-red-700 hidden">
                                Supprimer
                            </button>
                            <button type="submit"
                                    class="px-4 py-2 text-sm font-medium text-white bg-blue-600 rounded-md hover:bg-blue-700">
                                Enregistrer
                            </button>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block javascripts %}
    {{ parent() }}
    <script src='https://cdn.jsdelivr.net/npm/fullcalendar@6.1.10/index.global.min.js'></script>
    <script>
        // Configuration des URLs pour l'API - accessible globalement
        window.calendarApiUrls = {
            segments: "{{ path('api_segment_index') }}",
            missions: "{{ path('api_mission_index') }}",
            users: "{{ path('api_user_index') }}",
            segmentUpdate: "{{ path('api_segment_update', {id: '__ID__'}) }}".replace('__ID__', ''),
            segmentDelete: "{{ path('api_segment_delete', {id: '__ID__'}) }}".replace('__ID__', ''),
            segmentCreate: "{{ path('api_segment_create') }}",
            segmentCalendar: "{{ path('api_segment_calendar') }}",
            segmentValidate: "{{ path('api_segment_validate', {id: '__ID__'}) }}",
            segmentInvalidate: "{{ path('api_segment_invalidate', {id: '__ID__'}) }}"
        };

        // Fonctions utilitaires globales pour les modales (compatibles Turbo)
        window.openSegmentModal = function() {
            console.log('openSegmentModal appelée');
            if (window.calendarController) {
                window.calendarController.openSegmentModal();
            }
        };




    </script>
{% endblock %}
