import { Controller } from "@hotwired/stimulus";

export default class extends Controller {
    static targets = [
        "calendar", "modal", "modalTitle", "form", "segmentId",
        "userDropdown", "missionDropdown", "typeDropdown",
        "dateHeureDebut", "dateHeureFin", "deleteButton",
        "userFilterDropdown", "clearFiltersButton", "selectedUsersDisplay",
        "validationButtons", "validateButton", "invalidateButton"
    ];

    static values = {
        currentUser: Object
    };

    static get optionalTargets() {
        return [
            "modal", "modalTitle", "form", "segmentId",
            "userDropdown", "missionDropdown", "typeDropdown",
            "dateHeureDebut", "dateHeureFin", "deleteButton",
            "userFilterDropdown", "clearFiltersButton", "selectedUsersDisplay",
            "validationButtons", "validateButton", "invalidateButton"
        ];
    }

    connect() {

        // Nettoyer toute instance précédente si elle existe
        if (window.calendarController && window.calendarController !== this) {
            if (window.calendarController.calendar) {
                window.calendarController.calendar.destroy();
            }
        }

        // Stocker la référence du contrôleur globalement pour les fonctions onclick
        window.calendarController = this;

        this.calendar = null;
        this.currentSegmentId = null;
        this.allUsers = [];
        this.allMissions = [];
        this.globalDropdownListener = null;
        this.userChangeHandler = null;
        this.selectedUserIds = new Set(); // Pour le filtrage des utilisateurs

        // Vérifier que FullCalendar est disponible avant de continuer
        if (typeof FullCalendar === 'undefined') {
            // Attendre que FullCalendar soit chargé
            const checkFullCalendar = () => {
                if (typeof FullCalendar !== 'undefined') {
                    this.loadData();
                } else {
                    setTimeout(checkFullCalendar, 100);
                }
            };
            checkFullCalendar();
        } else {
            // Charger les données et initialiser le calendrier
            this.loadData();
        }
    }

    disconnect() {
        // Nettoyer les ressources lors de la déconnexion
        if (this.calendar) {
            this.calendar.destroy();
            this.calendar = null;
        }

        // Nettoyer les écouteurs d'événements
        this.removeGlobalDropdownListener();
        this.removeDropdownToggleListeners();

        if (this.userChangeHandler) {
            const hiddenInput = this.hasUserDropdownTarget ?
                this.userDropdownTarget.querySelector('input[name="userId"]') : null;
            if (hiddenInput) {
                hiddenInput.removeEventListener('change', this.userChangeHandler);
            }
        }

        // Nettoyer la référence globale
        if (window.calendarController === this) {
            window.calendarController = null;
        }
    }

    async loadData() {
        try {
            // Toujours charger les données car elles seront nécessaires pour la modale
            const promises = [
                this.loadUsers(),
                this.loadMissions()
            ];

            await Promise.all(promises);

            // Initialiser le calendrier après le chargement des données
            this.initializeCalendar();

            // Initialiser le sélecteur d'utilisateurs pour les responsables de mission
            this.initializeUserFilter();
        } catch (error) {
            console.error('Erreur lors du chargement des données:', error);
            // Initialiser le calendrier même en cas d'erreur de chargement des données
            this.initializeCalendar();
        }
    }

    async loadUsers() {
        try {
            const response = await ajax.get(window.calendarApiUrls.users);
            this.allUsers = response.data.data || response.data || [];
        } catch (error) {
            console.error('Erreur lors du chargement des utilisateurs:', error);
        }
    }

    populateUserDropdown() {
        if (!this.hasUserDropdownTarget) {
            return;
        }

        // Attendre que le contrôleur soit initialisé
        setTimeout(() => {
            const userController = this.application.getControllerForElementAndIdentifier(this.userDropdownTarget, 'searchable-dropdown');

            if (!userController) {
                // Essayer d'accéder directement via l'élément
                if (this.userDropdownTarget.searchableDropdownController) {
                    userController = this.userDropdownTarget.searchableDropdownController;
                }
            }

            if (!userController) {
                // Créer les options manuellement
                this.createUserOptionsManually();
                return;
            }
            this.setupUserDropdown(userController);
        }, 500);
    }

    setupUserDropdown(userController) {
        // Si l'utilisateur connecté est un "user", le présélectionner et désactiver le champ
        if (this.currentUserValue && this.currentUserValue.isUser) {

            // Désactiver le bouton
            const button = this.userDropdownTarget.querySelector('[data-searchable-dropdown-target="button"]');
            if (button) {
                button.disabled = true;
                button.classList.add('bg-gray-100', 'cursor-not-allowed', 'opacity-60');
                button.title = 'Vous ne pouvez créer des segments que pour vous-même';
            }

            // Désactiver le champ de recherche
            const searchInput = this.userDropdownTarget.querySelector('[data-searchable-dropdown-target="searchInput"]');
            if (searchInput) {
                searchInput.disabled = true;
                searchInput.classList.add('bg-gray-100', 'cursor-not-allowed');
                searchInput.placeholder = 'Champ désactivé';
            }

            // Créer une option unique pour l'utilisateur connecté
            const userOption = {
                value: this.currentUserValue.id.toString(),
                text: this.currentUserValue.nomComplet,
                searchableText: this.currentUserValue.nomComplet,
                html: `
                    <span class="custom-dropdown-icon">👤</span>
                    ${this.currentUserValue.nomComplet} <span class="text-xs text-gray-500">(Vous)</span>
                `
            };

            userController.updateOptions([userOption]);
            userController.selectOption(this.currentUserValue.id.toString(), true);
            return;
        }

        // Pour les autres utilisateurs (admin/manager), peupler normalement
        const userOptions = this.allUsers.map(user => ({
            value: user.id.toString(),
            text: `${user.prenom} ${user.nom}`,
            searchableText: `${user.prenom} ${user.nom} ${user.role}`,
            html: `
                <span class="custom-dropdown-icon">👤</span>
                ${user.prenom} ${user.nom} <span class="text-xs text-gray-500">(${user.role})</span>
            `
        }));

        userController.updateOptions(userOptions);
    }

    createUserOptionsManually() {
        console.log('Création manuelle des options utilisateur');
        console.log('currentUser dans createUserOptionsManually:', this.currentUserValue);

        const optionsContainer = this.userDropdownTarget.querySelector('.options-container');
        if (!optionsContainer) {
            console.log('Pas de conteneur d\'options trouvé');
            return;
        }

        optionsContainer.innerHTML = '';

        // Si l'utilisateur connecté est un "user", désactiver le champ et le pré-remplir
        if (this.currentUserValue && this.currentUserValue.isUser) {
            console.log('Utilisateur avec rôle USER détecté, désactivation du champ (manuel)');

            // Désactiver le bouton
            const button = this.userDropdownTarget.querySelector('[data-searchable-dropdown-target="button"]');
            if (button) {
                button.disabled = true;
                button.classList.add('bg-gray-100', 'cursor-not-allowed', 'opacity-60');
                button.title = 'Vous ne pouvez créer des segments que pour vous-même';
            }

            // Désactiver le champ de recherche
            const searchInput = this.userDropdownTarget.querySelector('[data-searchable-dropdown-target="searchInput"]');
            if (searchInput) {
                searchInput.disabled = true;
                searchInput.classList.add('bg-gray-100', 'cursor-not-allowed');
                searchInput.placeholder = 'Champ désactivé';
            }

            // Créer une seule option pour l'utilisateur connecté
            const userButton = document.createElement('button');
            userButton.type = 'button';
            userButton.className = 'custom-dropdown-option bg-blue-50 text-blue-700';
            userButton.dataset.value = this.currentUserValue.id;
            userButton.dataset.text = this.currentUserValue.nomComplet;
            userButton.innerHTML = `
                <span class="custom-dropdown-icon">👤</span>
                ${this.currentUserValue.nomComplet} <span class="text-xs text-gray-500">(Vous)</span>
            `;

            userButton.addEventListener('click', () => {
                this.selectUserManually(this.currentUserValue.id, this.currentUserValue.nomComplet);
            });

            optionsContainer.appendChild(userButton);

            // Pré-sélectionner automatiquement
            this.selectUserManually(this.currentUserValue.id, this.currentUserValue.nomComplet);

            console.log('Champ utilisateur pré-rempli et désactivé (manuel) pour:', this.currentUserValue.nomComplet);
            return;
        }

        // Pour les autres utilisateurs (admin/manager), peupler normalement
        this.allUsers.forEach(user => {
            const button = document.createElement('button');
            button.type = 'button';
            button.className = 'custom-dropdown-option';
            button.dataset.value = user.id;
            button.dataset.text = `${user.prenom} ${user.nom}`;
            button.innerHTML = `
                <span class="custom-dropdown-icon">👤</span>
                ${user.prenom} ${user.nom} <span class="text-xs text-gray-500">(${user.role})</span>
            `;

            // Ajouter l'événement de clic manuellement
            button.addEventListener('click', () => {
                console.log('Utilisateur sélectionné manuellement:', user.id);
                this.selectUserManually(user.id, `${user.prenom} ${user.nom}`);
            });

            optionsContainer.appendChild(button);
        });

        console.log('Options utilisateur créées manuellement');
    }

    selectUserManually(userId, userName) {
        // Mettre à jour le bouton
        const buttonText = this.userDropdownTarget.querySelector('[data-searchable-dropdown-target="buttonText"]');
        if (buttonText) {
            buttonText.textContent = userName;
        }

        // Mettre à jour l'input caché
        const hiddenInput = this.userDropdownTarget.querySelector('input[name="userId"]');
        if (hiddenInput) {
            hiddenInput.value = userId;
            hiddenInput.dispatchEvent(new Event('change', { bubbles: true }));
        }

        // Fermer le dropdown
        const dropdown = this.userDropdownTarget.querySelector('[data-searchable-dropdown-target="dropdown"]');
        if (dropdown) {
            dropdown.classList.add('hidden');
        }

        console.log('Utilisateur sélectionné manuellement:', userId, userName);
    }

    async loadMissions() {
        try {
            console.log('Chargement des missions...');
            const response = await ajax.get(window.calendarApiUrls.missions);
            console.log('Réponse brute API missions:', response);

            // Essayer différents formats de réponse
            if (response.data && Array.isArray(response.data)) {
                this.allMissions = response.data;
            } else if (response.data && response.data.data && Array.isArray(response.data.data)) {
                this.allMissions = response.data.data;
            } else if (Array.isArray(response)) {
                this.allMissions = response;
            } else {
                console.error('Format de réponse missions inattendu:', response);
                this.allMissions = [];
            }

            console.log('Missions chargées:', this.allMissions);
            console.log('Nombre de missions:', this.allMissions.length);
        } catch (error) {
            console.error('Erreur lors du chargement des missions:', error);
        }
    }

    populateAllMissions() {
        console.log('populateAllMissions appelée');
        console.log('hasMissionDropdownTarget:', this.hasMissionDropdownTarget);
        console.log('allMissions:', this.allMissions);

        if (!this.hasMissionDropdownTarget) {
            console.log('Pas de missionDropdownTarget');
            return;
        }

        // Attendre que le contrôleur soit initialisé
        setTimeout(() => {
            const missionController = this.application.getControllerForElementAndIdentifier(this.missionDropdownTarget, 'searchable-dropdown');
            if (!missionController) {
                console.log('Pas de contrôleur searchable-dropdown trouvé pour mission');
                return;
            }
            this.setupMissionDropdown(missionController);
        }, 100);
    }

    setupMissionDropdown(missionController) {

        // Préparer les options pour le dropdown
        const missionOptions = this.allMissions.map(mission => {
            // Afficher les utilisateurs assignés
            let assignedUsers = '';
            if (mission.users && mission.users.length > 0) {
                assignedUsers = mission.users.map(u => `${u.prenom} ${u.nom}`).join(', ');
            } else {
                assignedUsers = 'Non assigné';
            }

            return {
                value: mission.id.toString(),
                text: mission.titre,
                searchableText: `${mission.titre} ${mission.pays} ${assignedUsers}`,
                html: `
                    <span class="custom-dropdown-icon">📋</span>
                    <div>
                        <div class="font-medium">${mission.titre}</div>
                        <div class="text-xs text-gray-500">${mission.pays} - ${assignedUsers}</div>
                    </div>
                `
            };
        });

        missionController.updateOptions(missionOptions);

        console.log('Mission dropdown peuplé avec', this.allMissions.length, 'missions');
    }

    filterMissionsByUser(userId) {
        if (!this.hasMissionDropdownTarget) return;

        console.log('Utilisateur sélectionné:', userId);

        // Attendre que le contrôleur soit initialisé
        setTimeout(() => {
            const missionController = this.application.getControllerForElementAndIdentifier(this.missionDropdownTarget, 'searchable-dropdown');
            if (!missionController) {
                console.log('Pas de contrôleur mission trouvé pour filtrage');
                return;
            }
            this.doFilterMissionsByUser(userId, missionController);
        }, 100);
    }

    doFilterMissionsByUser(userId, missionController) {

        if (!userId) {
            // Si aucun utilisateur sélectionné, afficher toutes les missions
            this.setupMissionDropdown(missionController);
            return;
        }

        const userMissions = this.allMissions.filter(mission => {
            // Vérifier si l'utilisateur est assigné à cette mission
            return mission.users && mission.users.some(user => user.id == userId);
        });

        console.log(`Missions filtrées pour l'utilisateur ${userId}:`, userMissions);

        if (userMissions.length === 0) {
            missionController.updateOptions([{
                value: '',
                text: 'Aucune mission assignée à cet utilisateur',
                html: `
                    <span class="custom-dropdown-icon">❌</span>
                    <span class="text-gray-500">Aucune mission assignée à cet utilisateur</span>
                `
            }]);
            return;
        }

        // Préparer les options filtrées
        const filteredOptions = userMissions.map(mission => ({
            value: mission.id.toString(),
            text: mission.titre,
            searchableText: `${mission.titre} ${mission.pays}`,
            html: `
                <span class="custom-dropdown-icon">📋</span>
                <div>
                    <div class="font-medium">${mission.titre}</div>
                    <div class="text-xs text-gray-500">${mission.pays}</div>
                </div>
            `
        }));

        missionController.updateOptions(filteredOptions);
    }

    initializeCalendar() {
        console.log('Élément calendrier trouvé:', this.calendarTarget);
        console.log('FullCalendar disponible:', typeof FullCalendar);

        if (!this.calendarTarget) {
            console.error('Élément calendrier non trouvé !');
            return;
        }

        if (typeof FullCalendar === 'undefined') {
            console.error('FullCalendar non chargé !');
            return;
        }

        this.calendar = new FullCalendar.Calendar(this.calendarTarget, {
            initialView: 'timeGridWeek',
            locale: 'fr',
            timeZone: 'local',
            headerToolbar: {
                left: 'prev,next today',
                center: 'title',
                right: 'dayGridMonth,timeGridWeek,timeGridDay'
            },
            height: 'auto',
            slotMinTime: '06:00:00',
            slotMaxTime: '22:00:00',
            businessHours: [
                {
                    daysOfWeek: [1, 2, 3, 4, 5],
                    startTime: '09:00',
                    endTime: '12:00'
                },
                {
                    daysOfWeek: [1, 2, 3, 4, 5],
                    startTime: '14:00',
                    endTime: '18:00'
                }
            ],
            selectable: true,
            selectMirror: true,
            editable: true,
            eventResizableFromStart: true,
            events: (info, successCallback, failureCallback) => {
                const url = window.calendarApiUrls.segmentCalendar + '?start=' + encodeURIComponent(info.startStr) + '&end=' + encodeURIComponent(info.endStr);
                console.log('Chargement des événements depuis:', url);
                ajax.get(url)
                .then(response => {
                    console.log('Événements reçus:', response);
                    successCallback(response.data);
                })
                .catch(error => {
                    console.error('Erreur lors du chargement des événements:', error);
                    failureCallback(error);
                });
            },
            select: async (info) => {
                await this.openSegmentModal();
                if (this.hasDateHeureDebutTarget) {
                    this.dateHeureDebutTarget.value = info.startStr.slice(0, 16);
                }
                if (this.hasDateHeureFinTarget) {
                    this.dateHeureFinTarget.value = info.endStr.slice(0, 16);
                }
            },
            eventClick: async (info) => {
                await this.editSegment(info.event);
            },
            eventDrop: (info) => {
                this.updateSegmentDates(info.event);
            },
            eventResize: (info) => {
                this.updateSegmentDates(info.event);
            }
        });

        this.calendar.render();
        console.log('Calendrier rendu');
    }

    async openSegmentModal() {
        console.log('openSegmentModal appelée');
        this.currentSegmentId = null;

        // Charger les données si elles ne sont pas encore chargées
        await this.ensureDataLoaded();

        if (this.hasModalTitleTarget) {
            this.modalTitleTarget.textContent = 'Ajouter un segment';
        }

        if (this.hasDeleteButtonTarget) {
            this.deleteButtonTarget.classList.add('hidden');
        }

        this.openModal();
    }

    async ensureDataLoaded() {
        // Charger les données seulement si nécessaire
        const promises = [];

        if (this.allUsers.length === 0) {
            promises.push(this.loadUsers());
        }

        if (this.allMissions.length === 0) {
            promises.push(this.loadMissions());
        }

        if (promises.length > 0) {
            try {
                await Promise.all(promises);
            } catch (error) {
                console.error('Erreur lors du chargement des données:', error);
            }
        }

        // Attendre un peu que la modale soit complètement rendue
        setTimeout(() => {
            // Peupler les dropdowns
            if (this.hasUserDropdownTarget && this.allUsers.length > 0) {
                this.populateUserDropdown();
            }

            if (this.hasMissionDropdownTarget && this.allMissions.length > 0) {
                this.populateAllMissions();
            }

            // Ajouter l'écouteur d'événements pour le changement d'utilisateur
            this.setupUserChangeListener();
        }, 200);
    }

    setupUserChangeListener() {
        if (this.hasUserDropdownTarget) {
            const hiddenInput = this.userDropdownTarget.querySelector('input[name="userId"]');
            if (hiddenInput) {
                // Supprimer l'ancien écouteur s'il existe
                hiddenInput.removeEventListener('change', this.userChangeHandler);

                // Créer et ajouter le nouvel écouteur
                this.userChangeHandler = (event) => {
                    console.log('Changement d\'utilisateur détecté:', event.target.value);
                    this.filterMissionsByUser(event.target.value);

                    // Réinitialiser la sélection de mission quand l'utilisateur change
                    if (this.hasMissionDropdownTarget) {
                        const missionController = this.application.getControllerForElementAndIdentifier(this.missionDropdownTarget, 'searchable-dropdown');
                        if (missionController) {
                            missionController.reset();
                        }
                    }
                };

                hiddenInput.addEventListener('change', this.userChangeHandler);
            }
        }

        // Ajouter des écouteurs pour fermer les dropdowns quand on clique sur d'autres éléments
        this.setupDropdownCloseListeners();
    }

    setupDropdownCloseListeners() {
        // Ajouter des écouteurs sur tous les inputs de la modale
        if (this.hasFormTarget) {
            const inputs = this.formTarget.querySelectorAll('input[type="datetime-local"]');
            inputs.forEach(input => {
                input.addEventListener('focus', () => {
                    this.closeAllDropdowns();
                });
            });
        }

        // Ajouter des écouteurs sur les boutons des dropdowns pour fermer les autres
        this.setupDropdownToggleListeners();
    }

    setupDropdownToggleListeners() {
        const searchableDropdowns = [
            { target: this.hasUserDropdownTarget ? this.userDropdownTarget : null, name: 'user', type: 'searchable-dropdown' },
            { target: this.hasMissionDropdownTarget ? this.missionDropdownTarget : null, name: 'mission', type: 'searchable-dropdown' }
        ].filter(item => item.target !== null);

        const customDropdowns = [
            { target: this.hasTypeDropdownTarget ? this.typeDropdownTarget : null, name: 'type', type: 'custom-dropdown' }
        ].filter(item => item.target !== null);

        const allDropdowns = [...searchableDropdowns, ...customDropdowns];

        allDropdowns.forEach((dropdown, index) => {
            const buttonSelector = dropdown.type === 'searchable-dropdown' ?
                '[data-searchable-dropdown-target="button"]' :
                '[data-custom-dropdown-target="button"]';

            const button = dropdown.target.querySelector(buttonSelector);
            if (button) {
                // Créer un handler unique pour chaque dropdown
                const handler = (event) => {
                    // Petite temporisation pour laisser le dropdown s'ouvrir d'abord
                    setTimeout(() => {
                        // Fermer tous les autres dropdowns
                        allDropdowns.forEach((otherDropdown, otherIndex) => {
                            if (otherIndex !== index) {
                                const controller = this.application.getControllerForElementAndIdentifier(otherDropdown.target, otherDropdown.type);
                                if (controller && controller.isOpen) {
                                    controller.close();
                                }
                            }
                        });
                    }, 10);
                };

                // Stocker le handler pour pouvoir le supprimer plus tard
                button._calendarDropdownHandler = handler;
                button.addEventListener('click', handler);
            }
        });
    }

    closeAllDropdowns() {
        // Fermer tous les dropdowns ouverts dans la modale
        const searchableDropdowns = [
            this.hasUserDropdownTarget ? this.userDropdownTarget : null,
            this.hasMissionDropdownTarget ? this.missionDropdownTarget : null
        ].filter(Boolean);

        const customDropdowns = [
            this.hasTypeDropdownTarget ? this.typeDropdownTarget : null
        ].filter(Boolean);

        // Fermer les dropdowns searchable
        searchableDropdowns.forEach(dropdown => {
            const controller = this.application.getControllerForElementAndIdentifier(dropdown, 'searchable-dropdown');
            if (controller && controller.isOpen) {
                controller.close();
            }
        });

        // Fermer les dropdowns custom
        customDropdowns.forEach(dropdown => {
            const controller = this.application.getControllerForElementAndIdentifier(dropdown, 'custom-dropdown');
            if (controller && controller.isOpen) {
                controller.close();
            }
        });
    }

    addGlobalDropdownListener() {
        // Créer l'écouteur s'il n'existe pas déjà
        if (!this.globalDropdownListener) {
            this.globalDropdownListener = (event) => {
                // Vérifier si le clic est en dehors des dropdowns
                const dropdowns = [
                    this.hasUserDropdownTarget ? this.userDropdownTarget : null,
                    this.hasMissionDropdownTarget ? this.missionDropdownTarget : null,
                    this.hasTypeDropdownTarget ? this.typeDropdownTarget : null
                ].filter(Boolean);

                const clickedInsideDropdown = dropdowns.some(dropdown =>
                    dropdown && dropdown.contains(event.target)
                );

                if (!clickedInsideDropdown) {
                    this.closeAllDropdowns();
                }
            };
        }

        // Ajouter l'écouteur au document
        document.addEventListener('click', this.globalDropdownListener);
    }

    removeGlobalDropdownListener() {
        if (this.globalDropdownListener) {
            document.removeEventListener('click', this.globalDropdownListener);
        }
    }

    removeDropdownToggleListeners() {
        const searchableDropdowns = [
            this.hasUserDropdownTarget ? this.userDropdownTarget : null,
            this.hasMissionDropdownTarget ? this.missionDropdownTarget : null
        ].filter(Boolean);

        const customDropdowns = [
            this.hasTypeDropdownTarget ? this.typeDropdownTarget : null
        ].filter(Boolean);

        // Supprimer les écouteurs des dropdowns searchable
        searchableDropdowns.forEach(dropdown => {
            const button = dropdown.querySelector('[data-searchable-dropdown-target="button"]');
            if (button && button._calendarDropdownHandler) {
                button.removeEventListener('click', button._calendarDropdownHandler);
                delete button._calendarDropdownHandler;
            }
        });

        // Supprimer les écouteurs des dropdowns custom
        customDropdowns.forEach(dropdown => {
            const button = dropdown.querySelector('[data-custom-dropdown-target="button"]');
            if (button && button._calendarDropdownHandler) {
                button.removeEventListener('click', button._calendarDropdownHandler);
                delete button._calendarDropdownHandler;
            }
        });
    }

    openModal() {
        console.log('openModal appelée');
        if (this.hasModalTarget) {
            this.modalTarget.classList.remove('hidden');

            // Ajouter l'animation d'entrée
            const modalContent = this.modalTarget.querySelector('.modal-content');
            if (modalContent) {
                modalContent.classList.add('modal-enter');
                // Retirer la classe d'animation après l'animation
                setTimeout(() => {
                    modalContent.classList.remove('modal-enter');
                }, 300);
            }

            // Ajouter un écouteur global pour fermer les dropdowns
            this.addGlobalDropdownListener();

            console.log('Modal ouvert');
        } else {
            console.error('Modal non trouvé');
        }
    }

    closeModal() {
        if (this.hasModalTarget) {
            this.modalTarget.classList.add('hidden');
        }

        // Fermer tous les dropdowns avant de fermer la modale
        this.closeAllDropdowns();

        // Supprimer les écouteurs
        this.removeGlobalDropdownListener();
        this.removeDropdownToggleListeners();

        // Déterminer si c'était une édition ou un nouveau segment
        const wasEditing = this.currentSegmentId !== null;
        this.resetForm(!wasEditing); // true = nouveau segment, false = édition
    }

    closeModalOnBackdrop(event) {
        // Fermer seulement si on clique sur l'arrière-plan (pas sur le contenu)
        if (event.target === event.currentTarget) {
            this.closeModal();
        }
    }

    resetForm(isNewSegment = true) {
        if (this.hasFormTarget) {
            this.formTarget.reset();
        }

        // Toujours remettre à null après fermeture
        this.currentSegmentId = null;

        // Réinitialiser tous les dropdowns searchable
        const dropdowns = [
            this.hasUserDropdownTarget ? this.userDropdownTarget : null,
            this.hasMissionDropdownTarget ? this.missionDropdownTarget : null
        ].filter(Boolean);

        dropdowns.forEach(dropdown => {
            const controller = this.application.getControllerForElementAndIdentifier(dropdown, 'searchable-dropdown');
            if (controller) {
                controller.reset();

                // Forcer la réinitialisation visuelle
                const buttonText = dropdown.querySelector('[data-searchable-dropdown-target="buttonText"]');
                if (buttonText) {
                    const placeholder = dropdown.dataset.searchableDropdownPlaceholderValue || 'Sélectionner une option';
                    buttonText.textContent = placeholder;
                }

                // Vider l'input caché
                const hiddenInput = dropdown.querySelector('input[type="hidden"]');
                if (hiddenInput) {
                    hiddenInput.value = '';
                }
            }
        });

        // Réinitialiser le dropdown de type (custom-dropdown)
        if (this.hasTypeDropdownTarget) {
            const typeController = this.application.getControllerForElementAndIdentifier(this.typeDropdownTarget, 'custom-dropdown');
            if (typeController) {
                typeController.selectedValue = '';
                if (typeController.hasButtonTextTarget) {
                    typeController.buttonTextTarget.textContent = 'Sélectionner un type';
                }
                if (typeController.hasHiddenInputTarget) {
                    typeController.hiddenInputTarget.value = '';
                }
                typeController.updateOptionStates('');
            }
        }

        // Réinitialiser le titre de la modale et le bouton supprimer
        if (this.hasModalTitleTarget) {
            this.modalTitleTarget.textContent = 'Ajouter un segment';
        }

        if (this.hasDeleteButtonTarget) {
            this.deleteButtonTarget.classList.add('hidden');
        }

        // Cacher les boutons de validation
        if (this.hasValidationButtonsTarget) {
            this.validationButtonsTarget.style.display = 'none';
        }

        // Si l'utilisateur connecté est un "user", le représélectionner SEULEMENT pour un nouveau segment
        if (isNewSegment && this.currentUserValue && this.currentUserValue.isUser && this.hasUserDropdownTarget) {
            setTimeout(() => {
                const userController = this.application.getControllerForElementAndIdentifier(this.userDropdownTarget, 'searchable-dropdown');
                if (userController) {
                    userController.selectOption(this.currentUserValue.id.toString(), true);
                    // Filtrer les missions pour cet utilisateur
                    this.filterMissionsByUser(this.currentUserValue.id.toString());
                }
            }, 200);
        }
    }

    async editSegment(event) {
        this.currentSegmentId = event.id;

        // Charger les données si elles ne sont pas encore chargées
        await this.ensureDataLoaded();

        // Ouvrir la modale AVANT de remplir les champs
        this.openModal();

        // Forcer le peuplement des dropdowns
        if (this.hasUserDropdownTarget && this.allUsers.length > 0) {
            this.populateUserDropdown();
        }

        if (this.hasMissionDropdownTarget && this.allMissions.length > 0) {
            this.populateAllMissions();
        }

        // Attendre que les dropdowns soient peuplés (populateUserDropdown utilise 500ms)
        await new Promise(resolve => setTimeout(resolve, 600));

        if (this.hasModalTitleTarget) {
            this.modalTitleTarget.textContent = 'Modifier le segment';
        }

        if (this.hasDeleteButtonTarget) {
            this.deleteButtonTarget.classList.remove('hidden');
        }

        // Remplir le formulaire avec les données de l'événement
        if (this.hasSegmentIdTarget) {
            this.segmentIdTarget.value = event.id;
        }



        // Sélectionner le type
        if (event.extendedProps.type && this.hasTypeDropdownTarget) {
            const typeController = this.application.getControllerForElementAndIdentifier(this.typeDropdownTarget, 'custom-dropdown');
            if (typeController) {
                typeController.selectOption(event.extendedProps.type, true);
            }
        }

        // Sélectionner l'utilisateur et filtrer les missions
        if (event.extendedProps.userId && this.hasUserDropdownTarget) {
            const userController = this.application.getControllerForElementAndIdentifier(this.userDropdownTarget, 'searchable-dropdown');
            if (userController) {
                userController.selectOption(event.extendedProps.userId.toString(), true);
                // Filtrer les missions pour cet utilisateur
                this.filterMissionsByUser(event.extendedProps.userId.toString());
            }
        }

        // Sélectionner la mission
        if (event.extendedProps.missionId && this.hasMissionDropdownTarget) {
            // Attendre un peu que les missions soient filtrées
            setTimeout(() => {
                const missionController = this.application.getControllerForElementAndIdentifier(this.missionDropdownTarget, 'searchable-dropdown');
                if (missionController) {
                    missionController.selectOption(event.extendedProps.missionId.toString(), true);
                }
            }, 200);
        }

        // Convertir les dates ISO en format datetime-local
        const dateDebut = new Date(event.start);
        const dateFin = new Date(event.end);

        // Formater pour datetime-local (YYYY-MM-DDTHH:mm)
        if (this.hasDateHeureDebutTarget) {
            this.dateHeureDebutTarget.value = this.formatDateTimeLocal(dateDebut);
        }

        if (this.hasDateHeureFinTarget) {
            this.dateHeureFinTarget.value = this.formatDateTimeLocal(dateFin);
        }

        // Afficher les boutons de validation si l'utilisateur a les droits
        this.updateValidationButtonsVisibility(event);
    }

    formatDateTimeLocal(date) {
        // La date vient de FullCalendar qui l'affiche déjà en heure locale
        // On utilise directement cette date sans conversion supplémentaire
        const year = date.getFullYear();
        const month = String(date.getMonth() + 1).padStart(2, '0');
        const day = String(date.getDate()).padStart(2, '0');
        const hours = String(date.getHours()).padStart(2, '0');
        const minutes = String(date.getMinutes()).padStart(2, '0');

        return `${year}-${month}-${day}T${hours}:${minutes}`;
    }

    async updateSegmentDates(event) {
        // Vérifier si le segment peut être modifié
        if (event.extendedProps && !event.extendedProps.canBeModified) {
            alert('Ce segment ne peut pas être modifié car il est validé et des primes associées ont été validées par l\'assistante RH');
            event.revert();
            return;
        }

        try {
            // Convertir les dates FullCalendar en UTC
            const dateDebut = new Date(event.start);
            const dateFin = new Date(event.end);

            await ajax.put(window.calendarApiUrls.segmentUpdate + event.id, {
                dateHeureDebut: dateDebut.toISOString(),
                dateHeureFin: dateFin.toISOString()
            });
        } catch (error) {
            console.error('Erreur lors de la mise à jour:', error);

            let errorMessage = 'Erreur lors de la mise à jour du segment';

            if (error.data && error.data.errors) {
                if (Array.isArray(error.data.errors)) {
                    errorMessage = error.data.errors.join('\n');
                } else {
                    errorMessage = error.data.errors;
                }
            } else if (error.data && error.data.error) {
                errorMessage = error.data.error;
            }

            alert(errorMessage);
            event.revert();
        }
    }

    async deleteSegment() {
        if (!this.currentSegmentId) return;

        if (confirm('Êtes-vous sûr de vouloir supprimer ce segment ?')) {
            try {
                await ajax.delete(window.calendarApiUrls.segmentDelete + this.currentSegmentId);
                this.calendar.refetchEvents();
                this.closeModal();
            } catch (error) {
                console.error('Erreur lors de la suppression:', error);
                alert('Erreur lors de la suppression du segment');
            }
        }
    }

    async submitForm(event) {
        event.preventDefault();

        if (!this.hasFormTarget) {
            console.error('Formulaire non trouvé');
            return;
        }

        const formData = new FormData(event.target);

        // Convertir les dates locales en UTC pour le serveur
        const dateDebut = new Date(formData.get('dateHeureDebut'));
        const dateFin = new Date(formData.get('dateHeureFin'));

        const data = {
            userId: parseInt(formData.get('userId')),
            missionId: parseInt(formData.get('missionId')),
            type: formData.get('type'),
            dateHeureDebut: dateDebut.toISOString(),
            dateHeureFin: dateFin.toISOString()
        };

        try {
            if (this.currentSegmentId) {
                await ajax.put(window.calendarApiUrls.segmentUpdate + this.currentSegmentId, data);
            } else {
                await ajax.post(window.calendarApiUrls.segmentCreate, data);
            }

            if (this.calendar) {
                this.calendar.refetchEvents();
            }
            this.closeModal();
        } catch (error) {
            console.error('Erreur lors de l\'enregistrement:', error);

            let errorMessage = 'Erreur lors de l\'enregistrement du segment';

            if (error.data && error.data.errors) {
                if (Array.isArray(error.data.errors)) {
                    errorMessage = error.data.errors.join('\n');
                } else {
                    errorMessage = error.data.errors;
                }
            }

            alert(errorMessage);
        }
    }

    // Méthodes pour le filtrage des utilisateurs

    /**
     * Initialise le sélecteur d'utilisateurs pour les responsables de mission
     */
    initializeUserFilter() {
        // Vérifier si l'utilisateur peut filtrer (responsable de mission ou super admin)
        if (!this.currentUserValue ||
            (!this.currentUserValue.isResponsableMission && !this.currentUserValue.isSuperAdmin)) {
            return;
        }

        if (!this.hasUserFilterDropdownTarget) {
            return;
        }

        // Attendre que le contrôleur multi-user-selector soit initialisé
        setTimeout(() => {
            const filterController = this.application.getControllerForElementAndIdentifier(
                this.userFilterDropdownTarget,
                'multi-user-selector'
            );

            if (filterController) {
                filterController.updateUsers(this.allUsers);
            }
        }, 300);
    }

    /**
     * Gère le changement de sélection d'utilisateurs
     */
    onUserSelectionChanged(event) {
        const selectedUserIds = event.detail.selectedUserIds;
        this.selectedUserIds = new Set(selectedUserIds);

        // Recharger les événements du calendrier avec le filtre
        this.refreshCalendarEvents();
    }

    /**
     * Vide les filtres utilisateurs
     */
    clearUserFilters() {
        this.selectedUserIds.clear();

        if (this.hasUserFilterDropdownTarget) {
            const filterController = this.application.getControllerForElementAndIdentifier(
                this.userFilterDropdownTarget,
                'multi-user-selector'
            );

            if (filterController) {
                filterController.clearSelection();
            }
        }

        // Recharger tous les événements
        this.refreshCalendarEvents();
    }

    /**
     * Rafraîchit les événements du calendrier avec les filtres appliqués
     */
    refreshCalendarEvents() {
        if (!this.calendar) {
            return;
        }

        // Construire l'URL avec les paramètres de filtre
        const baseUrl = window.calendarApiUrls.segmentCalendar;
        let url = baseUrl;

        // Ajouter les IDs des utilisateurs sélectionnés comme paramètre
        if (this.selectedUserIds.size > 0) {
            const userIds = Array.from(this.selectedUserIds).join(',');
            url += `?userIds=${userIds}`;
        }

        // Mettre à jour la source d'événements du calendrier
        this.calendar.removeAllEventSources();
        this.calendar.addEventSource({
            url: url,
            method: 'GET',
            failure: (error) => {
                console.error('Erreur lors du chargement des événements filtrés:', error);
            }
        });
    }

    // Méthodes de validation des segments

    updateValidationButtonsVisibility(event) {
        // Vérifier si l'utilisateur a les droits de validation
        if (!this.currentUserValue ||
            (!this.currentUserValue.isResponsableMission && !this.currentUserValue.isSuperAdmin)) {
            return; // Pas de droits, ne pas afficher les boutons
        }

        if (!this.hasValidationButtonsTarget || !this.hasValidateButtonTarget || !this.hasInvalidateButtonTarget) {
            return; // Targets non disponibles
        }

        // Vérifier si le statut de validation peut être modifié
        if (!event.extendedProps || !event.extendedProps.canValidationStatusBeChanged) {
            return; // Le statut ne peut pas être modifié, ne pas afficher les boutons
        }

        // Afficher les boutons de validation
        this.validationButtonsTarget.style.display = 'flex';

        // Configurer les boutons selon l'état de validation du segment
        const isValidated = event.extendedProps.valide;

        if (isValidated) {
            // Segment validé : afficher le bouton "Invalider"
            this.validateButtonTarget.style.display = 'none';
            this.invalidateButtonTarget.style.display = 'inline-flex';
        } else {
            // Segment non validé : afficher le bouton "Valider"
            this.validateButtonTarget.style.display = 'inline-flex';
            this.invalidateButtonTarget.style.display = 'none';
        }
    }

    async validateSegment() {
        if (!this.currentSegmentId) {
            console.error('Aucun segment sélectionné pour validation');
            return;
        }

        if (confirm('Êtes-vous sûr de vouloir valider ce segment ?')) {
            try {
                const url = window.calendarApiUrls.segmentValidate.replace('__ID__', this.currentSegmentId);
                const response = await ajax.post(url);

                // Afficher un message de succès
                alert(response.data.message || 'Segment validé avec succès');

                // Rafraîchir le calendrier et fermer la modale
                if (this.calendar) {
                    this.calendar.refetchEvents();
                }
                this.closeModal();
            } catch (error) {
                console.error('Erreur lors de la validation:', error);

                let errorMessage = 'Erreur lors de la validation du segment';
                if (error.data && error.data.error) {
                    errorMessage = error.data.error;
                }

                alert(errorMessage);
            }
        }
    }

    async invalidateSegment() {
        if (!this.currentSegmentId) {
            console.error('Aucun segment sélectionné pour invalidation');
            return;
        }

        if (confirm('Êtes-vous sûr de vouloir invalider ce segment ?')) {
            try {
                const url = window.calendarApiUrls.segmentInvalidate.replace('__ID__', this.currentSegmentId);
                const response = await ajax.post(url);

                // Afficher un message de succès
                alert(response.data.message || 'Segment invalidé avec succès');

                // Rafraîchir le calendrier et fermer la modale
                if (this.calendar) {
                    this.calendar.refetchEvents();
                }
                this.closeModal();
            } catch (error) {
                console.error('Erreur lors de l\'invalidation:', error);

                let errorMessage = 'Erreur lors de l\'invalidation du segment';
                if (error.data && error.data.error) {
                    errorMessage = error.data.error;
                }

                alert(errorMessage);
            }
        }
    }
}
